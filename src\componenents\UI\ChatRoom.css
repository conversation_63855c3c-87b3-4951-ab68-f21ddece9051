/* src/componenents/UI/ChatRoom.css */

.chatroom-container {
  display: flex;
  height: 100vh;
  background-color: var(--theme-background, var(--chat-background, #1F2937));
  font-family: 'Segoe UI', sans-serif;
  color: var(--theme-text, var(--chat-text, white));
}

/* Sidebar */
.sidebar {
  width: 300px;
  background-color: var(--theme-surface, var(--chat-surface, #374151));
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chat-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #a566ff;
}

/* Sidebar bottom */
.sidebar-bottom {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-top: 10px;
  border-top: 1px solid #222;
}

.sidebar-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  border: 2px solid #a566ff;
}

.sidebar-username {
  flex: 1;
  font-weight: 500;
}

.sidebar-settings {
  font-size: 1.2rem;
  cursor: pointer;
  color: #999;
}

/* Chat Area */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: var(--theme-background, var(--chat-background, #1F2937));
}

.message-box {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* Chat Bubble */
.chat-bubble {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.07);
  padding: 12px 16px;
  margin-bottom: 10px;
  border-radius: 16px;
  color: #f1f1f1;
  max-width: 65%;
  backdrop-filter: blur(6px);
  box-shadow: 0 0 8px rgba(165, 102, 255, 0.3);
}

/* Input Bar */
.input-bar {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: var(--theme-surface, var(--chat-surface, #374151));
  border-top: 1px solid var(--theme-borderMuted, var(--chat-borderMuted, #4B5563));
  position: relative;
}

.emoji-bar {
  display: flex;
  gap: 12px;
  margin-right: 10px;
}

.emoji-icon,
.gif-icon {
  font-size: 1.5rem;
  cursor: pointer;
  color: #f5f5f5;
  transition: color 0.2s ease;
}

.emoji-icon:hover,
.gif-icon:hover {
  color: #a566ff;
}

/* Emoji Picker Positioning */
.emoji-picker {
  position: absolute;
  bottom: 60px;
  left: 10px;
  z-index: 5;
}

/* Message Input */
.message-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  border: none;
  outline: none;
  padding: 10px 14px;
  border-radius: 10px;
  font-size: 1rem;
  margin-right: 10px;
  backdrop-filter: blur(8px);
  box-shadow: inset 0 0 3px rgba(255, 255, 255, 0.1);
}

/* Send Button */
.send-btn {
  background: linear-gradient(90deg, #a566ff, #5d9fff);
  border: none;
  padding: 10px 18px;
  border-radius: 10px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: 0.2s ease;
}

.send-btn:hover {
  opacity: 0.85;
}
