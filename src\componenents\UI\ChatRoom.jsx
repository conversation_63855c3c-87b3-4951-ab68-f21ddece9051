import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import ZentroSidebar from "./ZentroSidebar";
import ProfilePanel from "./ProfilePanel";
import BlogSection from "../../components/Blog/BlogSection";
import DMView from "../../components/DM/DMView";
import ProfessionalDirectory from "./ProfessionalDirectory";
import ZentriumView from "../../components/AppHub/ZentriumView";
import { useUIEnhancements } from "../../hooks/useUIEnhancements";

const ChatRoom = () => {
  const location = useLocation();
  const { getResponsiveSpacing } = useUIEnhancements();
  const [currentView, setCurrentView] = useState(() => {
    // Set initial view based on current route
    if (location.pathname === '/blog') return 'blog';
    if (location.pathname === '/zentrium') return 'zentrium';
    if (location.pathname === '/groups') return 'groups';
    // Check for professional directory navigation
    if (location.state?.view === 'professional') return 'professional';
    return 'dm'; // Default to DM view
  });
  const [profileUser, setProfileUser] = useState(null);

  // Check for navigation state and route changes
  useEffect(() => {
    if (location.state?.selectedChat) {
      setCurrentView("dm");
    } else if (location.state?.view) {
      setCurrentView(location.state.view);
    } else if (location.pathname === '/blog') {
      setCurrentView('blog');
    } else if (location.pathname === '/zentrium') {
      setCurrentView('zentrium');
    } else if (location.pathname === '/groups') {
      setCurrentView('groups');
    }
  }, [location.state, location.pathname]);

  // Listen for blog view switch
  React.useEffect(() => {
    const handleSwitchToBlogView = () => {
      setCurrentView("blog");
    };

    window.addEventListener('switchToBlogView', handleSwitchToBlogView);

    return () => {
      window.removeEventListener('switchToBlogView', handleSwitchToBlogView);
    };
  }, []);

  // View switcher with enhanced animations
  const renderView = () => {
    const views = {
      "profile": (
        <ProfilePanel
          user={profileUser}
          onBack={() => setCurrentView("dm")}
        />
      ),
      "blog": <BlogSection />,
      "dm": <DMView />,
      "professional": (
        <ProfessionalDirectory
          onViewProfile={(professional) => {
            setProfileUser(professional);
            setCurrentView("profile");
          }}
        />
      ),
      "zentrium": <ZentriumView />
    };

    const ViewComponent = views[currentView] || <DMView />;

    return (
      <AnimatePresence mode="wait">
        <div
          key={currentView}
          style={{
            padding: getResponsiveSpacing('md'),
            minHeight: '100%',
            width: '100%'
          }}
        >
          {ViewComponent}
        </div>
      </AnimatePresence>
    );
  };

  return (
    <motion.div
      className="flex h-screen text-white relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%)'
      }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Desktop Sidebar */}
      <motion.div
        className="flex relative z-10"
        initial={{ x: -100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.4, delay: 0.1 }}
      >
        <ZentroSidebar currentView={currentView} setCurrentView={setCurrentView} />
      </motion.div>

      {/* Main Content */}
      <motion.div
        className="flex-1 relative z-10"
        style={{
          overflow: 'hidden',
          borderRadius: '0 20px 20px 0'
        }}
        initial={{ opacity: 0, scale: 0.98 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.4, delay: 0.2 }}
      >
        <div className="h-full overflow-y-auto">
          {renderView()}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ChatRoom;
