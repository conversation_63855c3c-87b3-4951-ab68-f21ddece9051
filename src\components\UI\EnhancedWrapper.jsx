import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useUIEnhancements } from '../../hooks/useUIEnhancements';

/**
 * Enhanced wrapper component that applies Instagram/Discord-style UI improvements
 * to any child component with seamless animations and consistent styling
 */
const EnhancedWrapper = ({
  children,
  type = 'page',
  className = '',
  style = {},
  enableAnimation = true,
  enableGlass = false,
  enableGradient = false,
  gradientType = 'primary',
  customAnimation = null,
  ...props
}) => {
  const {
    getSectionAnimation,
    getComponentStyles,
    getGradientStyles,
    getGlassMorphismStyles
  } = useUIEnhancements();

  // Determine animation based on type
  const getAnimation = () => {
    if (!enableAnimation) return {};
    if (customAnimation) return customAnimation;

    switch (type) {
      case 'page':
        return getSectionAnimation();
      case 'modal':
        return {
          initial: { opacity: 0, scale: 0.9 },
          animate: { opacity: 1, scale: 1 },
          exit: { opacity: 0, scale: 0.9 },
          transition: { duration: 0.2, ease: "easeOut" }
        };
      case 'card':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
          whileHover: { scale: 1.02, y: -2 },
          transition: { duration: 0.2 }
        };
      case 'list-item':
        return {
          initial: { opacity: 0, x: -20 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: 20 },
          transition: { duration: 0.2 }
        };
      default:
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 },
          transition: { duration: 0.2 }
        };
    }
  };

  // Combine all styles
  const finalStyles = {
    ...getComponentStyles(type),
    ...(enableGlass ? getGlassMorphismStyles() : {}),
    ...(enableGradient ? getGradientStyles(gradientType) : {}),
    ...style
  };

  const animation = getAnimation();

  return (
    <motion.div
      className={`enhanced-wrapper ${className}`}
      style={finalStyles}
      {...animation}
      {...props}
    >
      {children}
    </motion.div>
  );
};

/**
 * Enhanced page wrapper for main sections
 */
export const EnhancedPage = ({ children, section, ...props }) => (
  <EnhancedWrapper type="page" {...props}>
    <motion.div
      key={section}
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="w-full h-full"
    >
      {children}
    </motion.div>
  </EnhancedWrapper>
);

/**
 * Enhanced card wrapper with hover effects
 */
export const EnhancedCard = ({ children, ...props }) => (
  <EnhancedWrapper
    type="card"
    enableAnimation={true}
    className="enhanced-card"
    {...props}
  >
    {children}
  </EnhancedWrapper>
);

/**
 * Enhanced modal wrapper
 */
export const EnhancedModal = ({ children, isOpen, onClose, ...props }) => (
  <AnimatePresence>
    {isOpen && (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <EnhancedWrapper
          type="modal"
          enableGlass={true}
          onClick={(e) => e.stopPropagation()}
          {...props}
        >
          {children}
        </EnhancedWrapper>
      </motion.div>
    )}
  </AnimatePresence>
);

/**
 * Enhanced list wrapper with staggered animations
 */
export const EnhancedList = ({ children, stagger = 0.05, ...props }) => {
  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: stagger
          }
        }
      }}
      {...props}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          variants={{
            hidden: { opacity: 0, y: 20 },
            visible: { opacity: 1, y: 0 }
          }}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

/**
 * Enhanced button with interaction feedback
 */
export const EnhancedButton = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  ...props
}) => {
  const { getButtonProps, getGradientStyles } = useUIEnhancements();

  const variants = {
    primary: {
      ...getGradientStyles('primary'),
      color: 'white',
      border: 'none'
    },
    secondary: {
      backgroundColor: 'var(--theme-surface)',
      color: 'var(--theme-text)',
      border: '1px solid var(--theme-borderMuted)'
    },
    ghost: {
      backgroundColor: 'transparent',
      color: 'var(--theme-text)',
      border: '1px solid transparent'
    }
  };

  const sizes = {
    sm: { padding: '0.5rem 1rem', fontSize: '0.875rem' },
    md: { padding: '0.75rem 1.5rem', fontSize: '1rem' },
    lg: { padding: '1rem 2rem', fontSize: '1.125rem' }
  };

  return (
    <motion.button
      {...getButtonProps()}
      style={{
        ...variants[variant],
        ...sizes[size],
        opacity: disabled ? 0.6 : 1,
        cursor: disabled ? 'not-allowed' : 'pointer',
        borderRadius: '8px',
        fontWeight: '500',
        transition: 'all 0.2s ease'
      }}
      disabled={disabled || loading}
      whileHover={disabled ? {} : { scale: 1.05 }}
      whileTap={disabled ? {} : { scale: 0.95 }}
      {...props}
    >
      {loading ? (
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
        />
      ) : (
        children
      )}
    </motion.button>
  );
};

export default EnhancedWrapper;
