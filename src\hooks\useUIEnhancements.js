import { useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { uiEnhancements, applyUIEnhancements } from '../styles/uiEnhancements';
import { useMobileBehavior } from './useResponsive';

/**
 * Custom hook for Instagram/Discord-style UI enhancements
 * Provides seamless navigation, consistent styling, and smooth animations
 */
export const useUIEnhancements = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { shouldShowMobileLayout } = useMobileBehavior();
  
  const [currentSection, setCurrentSection] = useState('dm');
  const [navigationHistory, setNavigationHistory] = useState([]);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Track current section based on location
  useEffect(() => {
    const path = location.pathname;
    let section = 'dm';
    
    if (path.includes('/profile')) section = 'profile';
    else if (path.includes('/groups')) section = 'groups';
    else if (path.includes('/directory')) section = 'zentronet';
    else if (path.includes('/zentrium')) section = 'zentrium';
    else if (path.includes('/network')) section = 'network';
    
    setCurrentSection(section);
  }, [location]);

  // Seamless navigation with transitions
  const navigateWithTransition = useCallback((to, section = null) => {
    setIsTransitioning(true);
    
    // Add to navigation history
    setNavigationHistory(prev => [...prev.slice(-4), currentSection]);
    
    // Apply transition delay for smooth effect
    setTimeout(() => {
      navigate(to);
      setIsTransitioning(false);
    }, 150);
  }, [navigate, currentSection]);

  // Get animation props for current section
  const getSectionAnimation = useCallback(() => {
    return applyUIEnhancements.getNavigationTransition(currentSection);
  }, [currentSection]);

  // Get consistent component styles
  const getComponentStyles = useCallback((component, customStyles = {}) => {
    const baseStyles = applyUIEnhancements.getConsistentStyles(component);
    const mobileStyles = applyUIEnhancements.getMobileStyles(shouldShowMobileLayout);
    
    return {
      ...baseStyles,
      ...mobileStyles,
      ...customStyles
    };
  }, [shouldShowMobileLayout]);

  // Get animation props for different component types
  const getAnimationProps = useCallback((type) => {
    return applyUIEnhancements.getAnimationProps(type);
  }, []);

  // Enhanced card props with hover effects
  const getCardProps = useCallback((customProps = {}) => {
    return {
      ...getAnimationProps('cardHover'),
      style: getComponentStyles('card'),
      ...customProps
    };
  }, [getAnimationProps, getComponentStyles]);

  // Enhanced button props
  const getButtonProps = useCallback((customProps = {}) => {
    return {
      ...getAnimationProps('buttonHover'),
      style: getComponentStyles('button'),
      ...customProps
    };
  }, [getAnimationProps, getComponentStyles]);

  // Enhanced modal props
  const getModalProps = useCallback((customProps = {}) => {
    return {
      ...getAnimationProps('modalTransition'),
      style: getComponentStyles('modal'),
      ...customProps
    };
  }, [getAnimationProps, getComponentStyles]);

  // Enhanced list item props
  const getListItemProps = useCallback((index = 0, customProps = {}) => {
    return {
      ...getAnimationProps('listItem'),
      transition: { 
        ...getAnimationProps('listItem').transition,
        delay: index * 0.05 // Stagger animation
      },
      ...customProps
    };
  }, [getAnimationProps]);

  // Get theme-aware gradient styles
  const getGradientStyles = useCallback((type = 'primary') => {
    return {
      background: uiEnhancements.theming.gradients[type] || uiEnhancements.theming.gradients.primary
    };
  }, []);

  // Get glass morphism styles
  const getGlassMorphismStyles = useCallback((intensity = 1) => {
    const base = uiEnhancements.theming.glassMorphism;
    return {
      background: base.background,
      backdropFilter: `blur(${10 * intensity}px)`,
      border: base.border,
      borderRadius: uiEnhancements.consistency.borderRadius.medium
    };
  }, []);

  // Navigation utilities
  const canGoBack = navigationHistory.length > 0;
  const goBack = useCallback(() => {
    if (canGoBack) {
      const previousSection = navigationHistory[navigationHistory.length - 1];
      setNavigationHistory(prev => prev.slice(0, -1));
      
      // Navigate based on section
      const routes = {
        profile: '/profile',
        dm: '/',
        groups: '/groups',
        zentronet: '/directory',
        zentrium: '/zentrium',
        network: '/network'
      };
      
      navigateWithTransition(routes[previousSection] || '/');
    }
  }, [canGoBack, navigationHistory, navigateWithTransition]);

  // Mobile-specific utilities
  const getMobileNavStyles = useCallback(() => {
    if (!shouldShowMobileLayout) return {};
    
    return {
      position: 'fixed',
      bottom: 0,
      left: 0,
      right: 0,
      height: uiEnhancements.mobile.bottomNavHeight,
      zIndex: 50,
      ...getGlassMorphismStyles(0.8)
    };
  }, [shouldShowMobileLayout, getGlassMorphismStyles]);

  // Responsive spacing
  const getResponsiveSpacing = useCallback((size = 'md') => {
    const spacing = shouldShowMobileLayout 
      ? uiEnhancements.mobile.spacing 
      : uiEnhancements.consistency.spacing;
    
    return spacing[size] || spacing.md;
  }, [shouldShowMobileLayout]);

  return {
    // Navigation
    currentSection,
    navigateWithTransition,
    canGoBack,
    goBack,
    isTransitioning,
    
    // Animations
    getSectionAnimation,
    getAnimationProps,
    getCardProps,
    getButtonProps,
    getModalProps,
    getListItemProps,
    
    // Styling
    getComponentStyles,
    getGradientStyles,
    getGlassMorphismStyles,
    getMobileNavStyles,
    getResponsiveSpacing,
    
    // State
    shouldShowMobileLayout,
    navigationHistory
  };
};

export default useUIEnhancements;
