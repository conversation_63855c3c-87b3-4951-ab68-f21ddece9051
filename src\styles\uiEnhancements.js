// Instagram/Discord-Style UI Enhancement Algorithm
// Seamless navigation and visual consistency across all components

export const uiEnhancements = {
  // Animation configurations
  animations: {
    // Page transitions
    pageTransition: {
      initial: { opacity: 0, x: 20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: -20 },
      transition: { duration: 0.3, ease: "easeInOut" }
    },

    // Modal animations
    modalTransition: {
      initial: { opacity: 0, scale: 0.9 },
      animate: { opacity: 1, scale: 1 },
      exit: { opacity: 0, scale: 0.9 },
      transition: { duration: 0.2, ease: "easeOut" }
    },

    // Card hover effects
    cardHover: {
      whileHover: {
        scale: 1.02,
        y: -2,
        transition: { duration: 0.2 }
      },
      whileTap: { scale: 0.98 }
    },

    // Button interactions
    buttonHover: {
      whileHover: { scale: 1.05 },
      whileTap: { scale: 0.95 },
      transition: { duration: 0.1 }
    },

    // List item animations
    listItem: {
      initial: { opacity: 0, y: 10 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -10 },
      transition: { duration: 0.2 }
    }
  },



  // Visual consistency rules
  consistency: {
    // Border radius standards
    borderRadius: {
      small: '8px',
      medium: '12px',
      large: '16px',
      xl: '20px',
      full: '9999px'
    },

    // Shadow standards
    shadows: {
      subtle: '0 1px 3px rgba(0, 0, 0, 0.1)',
      medium: '0 4px 6px rgba(0, 0, 0, 0.1)',
      large: '0 10px 15px rgba(0, 0, 0, 0.1)',
      xl: '0 20px 25px rgba(0, 0, 0, 0.15)'
    },

    // Spacing system
    spacing: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      '2xl': '3rem'
    }
  },

  // Profile-to-everything navigation system
  navigation: {
    // Seamless transitions between sections
    sectionTransitions: {
      profile: { from: 'right', duration: 0.3 },
      dm: { from: 'left', duration: 0.3 },
      groups: { from: 'bottom', duration: 0.3 },
      zentronet: { from: 'top', duration: 0.3 },
      zentrium: { from: 'right', duration: 0.3 },
      network: { from: 'left', duration: 0.3 }
    },

    // Breadcrumb system
    breadcrumbs: {
      enabled: true,
      maxItems: 3
    }
  },

  // Theme integration
  theming: {
    // Dynamic color adaptation
    adaptiveColors: true,

    // Gradient overlays
    gradients: {
      primary: 'linear-gradient(135deg, var(--theme-primary), var(--theme-secondary))',
      surface: 'linear-gradient(135deg, var(--theme-surface), var(--theme-surfaceVariant))',
      accent: 'linear-gradient(135deg, var(--theme-accent), var(--theme-primary))'
    },

    // Glass morphism effects
    glassMorphism: {
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.2)'
    }
  }
};

// Utility functions for applying enhancements
export const applyUIEnhancements = {
  // Get animation props for components
  getAnimationProps: (type) => {
    return uiEnhancements.animations[type] || {};
  },



  // Get consistent styling
  getConsistentStyles: (element) => {
    const { borderRadius, shadows, spacing } = uiEnhancements.consistency;

    const baseStyles = {
      borderRadius: borderRadius.medium,
      padding: spacing.md,
      margin: spacing.sm
    };

    switch (element) {
      case 'card':
        return {
          ...baseStyles,
          boxShadow: shadows.medium,
          borderRadius: borderRadius.large
        };
      case 'button':
        return {
          ...baseStyles,
          borderRadius: borderRadius.small,
          padding: `${spacing.sm} ${spacing.md}`
        };
      case 'modal':
        return {
          ...baseStyles,
          boxShadow: shadows.xl,
          borderRadius: borderRadius.xl
        };
      default:
        return baseStyles;
    }
  },

  // Get navigation transition
  getNavigationTransition: (section) => {
    const transition = uiEnhancements.navigation.sectionTransitions[section];
    if (!transition) return uiEnhancements.animations.pageTransition;

    const direction = transition.from;
    const variants = {
      left: { initial: { x: -100 }, animate: { x: 0 }, exit: { x: 100 } },
      right: { initial: { x: 100 }, animate: { x: 0 }, exit: { x: -100 } },
      top: { initial: { y: -100 }, animate: { y: 0 }, exit: { y: 100 } },
      bottom: { initial: { y: 100 }, animate: { y: 0 }, exit: { y: -100 } }
    };

    return {
      ...variants[direction],
      transition: { duration: transition.duration, ease: "easeInOut" }
    };
  }
};

export default uiEnhancements;
